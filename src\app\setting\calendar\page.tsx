"use client";
import Button from "@/components/common/Button";
import Tabs from "@/components/common/Tabs";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import {
  createMultipleClients,
  eventCalendarSync,
  getCalendarEventClients,
  updateSyncStatus,
  useGetCalendarEvents,
  useGetResyncAllData,
} from "@/services/setting.service";
import { formatDate, formatTime } from "@/utils/axios";
import { Clock, EnvelopeSimple, Repeat } from "@phosphor-icons/react";
import moment from "moment";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import Modal from "@/components/common/Modal";
import THeader from "@/components/dashboard/common/table/THeader";
import CalendarTBody from "@/components/dashboard/common/table/CalendarTBody";
import SyncConfirmationModal from "@/components/common/SyncConfirmationModal";
import ConflictModal from "@/components/common/ConflictModal";
import RescheduledModal from "@/components/common/RescheduledModal";
import SyncingModal from "@/components/common/SyncingModal";
import { AxiosError } from "axios";

const CalendarTab = [
  { label: "Syncable Events", value: "Syncable Events" },
  { label: "Non Syncable Events", value: "Non Syncable Events" },
];

const CalendarTableHeader = [
  "S.no",
  "Session",
  "Email",
  "Client Name ",
  "Mobile Number",
  "Amount",
];

interface emails {
  email?: string;
  organizer?: boolean;
  self?: boolean;
}

interface Item {
  kind?: string;
  summary?: string;
  start?: {
    dateTime: string;
    date: string;
    timeZone: string;
  };
  originalStartTime: {
    dateTime: string;
  };
  creator: {
    email: string;
  };
  email: string;
  description: string;
  status: string;
  depression: string;
  eventIds?: string[] | undefined;
  id?: string;
  totalRecurrenceData?: number;
  firstRecurrenceData?: {
    status?: string;
    fromDate?: string;
  };
  attendees?: emails[];
  isRescheduled?: boolean;
}

interface TableRow {
  isPresent: boolean;
  session: string;
  email: string;
  clientName: string;
  mobile: number;
  amount: number;
}





interface EventData {
  calendarEventId: string;
  exists: boolean;
  summary: string;
  attendee: string;
  client?: {
    name: string;
    mobileNumber: string;
    Amount: number;
  };
}

const CalendarSetting = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(CalendarTab[0]);
  const [loading, setLoading] = useState(false);
  const [selectedEventIds, setSelectedEventIds] = useState<string[]>([]);
  const [tableData, setTableData] = useState<TableRow[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [updateDisabled, setUpdateDisabled] = useState(false);
  const [syncDisabled, setSyncDisabled] = useState(true);
  const [isUpdateDisabled, setIsUpdateDisabled] = useState(false);
  const { resyncCalendarData, resyncCalendarLoading } = useGetCalendarEvents();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    message: string;
    conflicts: { startTime: string; endTime: string; clientName?: string; clientId?: string }[];
  }>({
    message: "",
    conflicts: [],
  });
  const [isRescheduledModalOpen, setIsRescheduledModalOpen] = useState(false);
  const [isSyncAllChecked, setIsSyncAllChecked] = useState(false);
  const [isSyncingModalOpen, setIsSyncingModalOpen] = useState(false);

  // Auto-manage sync all checkbox state
  useEffect(() => {
    if (resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable)) {
      const confirmedEvents = resyncCalendarData.syncable.filter((event: Item) => !event.isRescheduled);
      const confirmedEventIds = confirmedEvents
        .map((event: Item) => event.id)
        .filter((id: string | undefined) => id !== undefined) as string[];

      // Check if all confirmed events are selected
      const allConfirmedSelected = confirmedEventIds?.length > 0 &&
        confirmedEventIds.every(id => selectedEventIds.includes(id));

      setIsSyncAllChecked(allConfirmedSelected);
    }
  }, [selectedEventIds, resyncCalendarData?.syncable]);

  const handleContinueClick = async () => {
    setUpdateDisabled(false);
    setSyncDisabled(true);

    try {
      const selectedEvents = resyncCalendarData?.syncable?.filter(
        (event: Item) => event.id && selectedEventIds.includes(event.id)
      );

      if (!selectedEvents?.length) {
        console.warn("No selected events found.");
        return;
      }

      // Check if any selected events are rescheduled
      const rescheduledEvents = selectedEvents.filter((event: Item) => event.isRescheduled);

      // If there are rescheduled events, show the rescheduled modal
      if (rescheduledEvents?.length > 0) {
        setIsRescheduledModalOpen(true);
        return;
      }

      const result = await getCalendarEventClients(selectedEvents);

      setIsModalOpen(true);

      // Prepare table data with only selected events
      const newTableData = result.events && Array.isArray(result.events)
        ? result.events
          .filter((event: EventData) =>
            selectedEventIds.includes(event.calendarEventId)
          )
          .map((event: EventData) => ({
            isPresent: event.exists,
            session: event.summary,
            email: event.attendee,
            clientName: event.exists && event.client ? event.client.name : "",
            mobile:
              event.exists && event.client
                ? Number(event.client.mobileNumber) || ""
                : "",
            amount:
              event.exists && event.client
                ? Number(event.client.Amount) || ""
                : "",
          }))
        : [];

      setTableData(newTableData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  // Validate form before updating
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    tableData.forEach((row, index) => {
      if (!row.clientName.trim()) {
        newErrors[`${index}-clientName`] = "Name is required*";
      }
      if (!row.amount) {
        newErrors[`${index}-amount`] = "Amount is required*";
      }

      const mobileStr = row.mobile?.toString().trim();
      if (mobileStr && !/^\d{10}$/.test(mobileStr)) {
        newErrors[`${index}-mobile`] =
          "Mobile number must be exactly 10 digits*";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleUpdate = async () => {
    if (!validateForm()) return;
    
    try {
      const clients = tableData.map((row) => ({
        name: row.clientName,
        defaultSessionAmount: row.amount.toString(),
        email: row.email,
        phone: row.mobile.toString(),
      }));

      await createMultipleClients(clients);
      setUpdateDisabled(true);
      setIsUpdateDisabled(true);
    } catch (error) {
      console.error("Update failed", error);
    }
  };

  const handleRemoveSelectedEvents = () => {
    setSelectedEventIds([]);
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: Item
  ) => {
    if (event.target.checked) {
      // Check if we're selecting a rescheduled event
      if (item.isRescheduled) {
        // If selecting rescheduled, clear any confirmed events
        const currentRescheduledEvents = (resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable))
          ? resyncCalendarData.syncable.filter(
              (evt: Item) => evt.isRescheduled && selectedEventIds.includes(evt.id || "")
            )
          : [];

        let newEventIds = [];
        if (item?.eventIds && item?.eventIds?.length > 0) {
          newEventIds = [...item?.eventIds];
        } else {
          newEventIds.push(item.id);
        }

        // Only keep other rescheduled events and add this one
        const otherRescheduledIds = currentRescheduledEvents
          .flatMap((evt: Item) => evt.eventIds || [evt.id])
          .filter((id: string | undefined) => id !== undefined) as string[];

        setSelectedEventIds([
          ...otherRescheduledIds,
          ...(newEventIds.filter((id) => id !== undefined) as string[]),
        ]);
      } else {
        // If selecting confirmed, clear any rescheduled events
        const currentConfirmedEvents = (resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable))
          ? resyncCalendarData.syncable.filter(
              (evt: Item) => !evt.isRescheduled && selectedEventIds.includes(evt.id || "")
            )
          : [];

        let newEventIds = [];
        if (item?.eventIds && item?.eventIds?.length > 0) {
          newEventIds = [...item?.eventIds];
        } else {
          newEventIds.push(item.id);
        }

        // Only keep other confirmed events and add this one
        const otherConfirmedIds = currentConfirmedEvents
          .flatMap((evt: Item) => evt.eventIds || [evt.id])
          .filter((id: string | undefined) => id !== undefined) as string[];

        setSelectedEventIds([
          ...otherConfirmedIds,
          ...(newEventIds.filter((id) => id !== undefined) as string[]),
        ]);
      }
    } else {
      let nIds = [];
      if (item?.eventIds && item?.eventIds?.length > 0) {
        nIds = selectedEventIds.filter(
          (selectedId) => !item?.eventIds?.includes(selectedId)
        );
      } else {
        nIds = selectedEventIds.filter((selectedId) => selectedId !== item?.id);
      }
      setSelectedEventIds(nIds);
    }
  };

  async function updateSyncEventStatus() {
    await updateSyncStatus().catch((err) => {
      console.log(err);
    });
  }

  const syncCalendarEvents = async () => {
    setLoading(true);

    if (selectedEventIds?.length <= 0) {
      toast.error("Please select at least one session");
      setLoading(false);
      return false;
    }

    try {
      const res = await eventCalendarSync({ eventIds: selectedEventIds });

      if (res.status === 200) {
        // Check if there are conflicts in the response
        if (res.data && res.data.success === false && res.data.conflicts) {
          // Handle conflicts
          setConflictData({
            message: res.data.message || "Conflicts found with existing sessions.",
            conflicts: res.data.conflicts || []
          });
          setIsConfirmationModalOpen(false);
          setTimeout(() => {
            setIsConflictModalOpen(true);
          }, 100);
          return false; // Return false to indicate conflicts
        }

        // No conflicts, sync was successful
        return true; // Return true to indicate success
      }
    } catch (err: unknown) {
      const axiosError = err as AxiosError;
      console.error(axiosError);

      // Check if the error response contains conflict data
      if (axiosError.response && axiosError.response.data &&
          typeof axiosError.response.data === 'object' &&
          'success' in axiosError.response.data &&
          axiosError.response.data.success === false &&
          'conflicts' in axiosError.response.data) {
        const errorData = axiosError.response.data as {
          message?: string;
          conflicts?: Array<{ startTime: string; endTime: string; clientName?: string; clientId?: string }>
        };
        setConflictData({
          message: errorData.message || "Conflicts found with existing sessions.",
          conflicts: errorData.conflicts || []
        });
        setIsConfirmationModalOpen(false);
        setTimeout(() => {
          setIsConflictModalOpen(true);
        }, 100);
        return false; // Return false to indicate conflicts
      } else {
        toast.error("Sync failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }

    return false; // Return false by default for error cases
  };

  const handleSync = async () => {
    try {
      if (selectedEventIds?.length <= 0) {
        toast.error("Please select at least one session", {
          position: "top-center",
        });
        return;
      }

      // Check if selected events are rescheduled
      const selectedEvents = (resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable))
        ? resyncCalendarData.syncable.filter(
            (event: Item) => event.id && selectedEventIds.includes(event.id)
          )
        : [];
      const hasRescheduledInSelection = selectedEvents?.some((event: Item) => event.isRescheduled);

      // Close the details modal first
      setIsModalOpen(false);

      if (hasRescheduledInSelection) {
        // Show rescheduled modal for rescheduled events
        setTimeout(() => {
          setIsRescheduledModalOpen(true);
        }, 100);
      } else {
        // For confirmed events, sync directly with syncing modal
        setTimeout(async () => {
          try {
            setLoading(true);
            setIsSyncingModalOpen(true);

            const syncResult = await syncCalendarEvents();

            if (syncResult) {
              // Keep syncing modal open for a moment to show completion
              setTimeout(() => {
                setIsSyncingModalOpen(false);
                toast.success("Calendar events synced successfully");
                updateSyncEventStatus();
                router.push("/session");
              }, 1500);
            } else {
              setIsSyncingModalOpen(false);
            }
          } catch (error) {
            console.error("Sync failed", error);
            setIsSyncingModalOpen(false);
            toast.error("Sync failed. Please try again.");
          } finally {
            setLoading(false);
            handleRemoveSelectedEvents();
          }
        }, 100);
      }
    } catch (error) {
      console.error("Sync failed", error);
      toast.error("Sync failed. Please try again.");
    }
  };

  // This function will be called after user confirms in the confirmation modal
  const handleConfirmSync = async () => {
    try {
      setLoading(true);
      setIsConfirmationModalOpen(false);
      setIsSyncingModalOpen(true);

      // Proceed with sync directly
      const syncResult = await syncCalendarEvents();

      // Only close modals if sync was successful
      if (syncResult) {
        setIsModalOpen(false);
        // Keep syncing modal open for a moment to show completion
        setTimeout(() => {
          setIsSyncingModalOpen(false);
          toast.success("Calendar events synced successfully");
          updateSyncEventStatus();
          router.push("/session");
        }, 1500);
      } else {
        setIsSyncingModalOpen(false);
      }
    } catch (error) {
      console.error("Sync failed", error);
      setIsSyncingModalOpen(false);
      toast.error("Sync failed. Please try again.");
      setIsConfirmationModalOpen(false);
    } finally {
      setLoading(false);
      handleRemoveSelectedEvents();
      setIsModalOpen(false);
    }
  };

  // Handler for rescheduled modal confirmation
  const handleRescheduledConfirm = () => {
    setIsRescheduledModalOpen(false);
    handleRemoveSelectedEvents();
  };

  return (
    <SettingLayout>
      <div>
        <h1 className="text-xl_30 font-semibold text-primary">
          Calendar Setting
        </h1>
        <div className="pt-6">
          <div className="flex flex-col sm:flex-row sm:justify-between gap-4 sm:gap-0">
            <Tabs
              tabs={CalendarTab}
              activeTab={activeTab?.label}
              setActiveTab={setActiveTab}
              handleRemoveSelectedEvents={handleRemoveSelectedEvents}
              sessionCount={
                activeTab?.label === "Syncable Events"
                  ? resyncCalendarData?.syncable?.length || 0
                  : resyncCalendarData?.not_syncable?.length || 0
              }
            />
            {activeTab?.label === "Syncable Events" &&
              resyncCalendarData?.syncable?.length > 0 && (
                <div className="flex items-center justify-center sm:justify-end p-3 sm:p-5">
                  <label htmlFor="syncAll" className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="syncAll"
                      className="w-4 h-4 accent-green-600 cursor-pointer"
                      checked={isSyncAllChecked}
                      onChange={(e) => {
                        if (e.target.checked) {
                          // Select only confirmed events (exclude rescheduled)
                          const confirmedEventIds = (resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable))
                            ? resyncCalendarData.syncable
                                .filter((event: Item) => !event.isRescheduled)
                                .map((event: Item) => event.id)
                                .filter(
                                  (id: string | undefined) => id !== undefined
                                ) as string[]
                            : [];
                          setSelectedEventIds(confirmedEventIds);
                        } else {
                          // Deselect all
                          setSelectedEventIds([]);
                        }
                      }}
                    />

                    <p className="text-sm sm:text-base/6 text-green-600 font-semibold capitalize">
                      Sync All
                    </p>
                  </label>
                </div>
              )}
          </div>
          {resyncCalendarLoading ? ( // Check if loading
            <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
              {/* Display skeletons for loading state */}
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="border border-green-600/20 rounded-base overflow-hidden animate-pulse"
                >
                  <div className="flex items-center justify-between p-5 bg-yellow-100">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                      <div className="h-6 w-1/3 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="h-6 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                  <div className="p-5">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="h-4 w-1/2 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-2/3 bg-gray-200 rounded-full"></div>
                      </div>
                      <div className="relative group">
                        <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                      </div>
                    </div>
                    <hr className="my-5" />
                    <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {activeTab?.label === "Syncable Events" && (
                <div>
                   {/* Explanatory note for syncable events */}
                  <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm text-gray-800">
                    We’ll sync all your new sessions seamlessly ✨ <br />
                    But if you need to reschedule or cancel, we recommend the platform to avoid any double bookings!
                    </p>
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4.5 py-4 sm:py-7.5">
                    {resyncCalendarData?.syncable && Array.isArray(resyncCalendarData.syncable) && resyncCalendarData?.syncable?.length > 0 &&
                      resyncCalendarData.syncable.map(
                        (item: Item, index: number) => {
                          const attendees = (item.attendees && Array.isArray(item.attendees))
                            ? item.attendees.filter(
                                (attend) => attend.self != true
                              )
                            : [];
                          return (
                            <div
                              key={index}
                              className={`border rounded-base overflow-hidden ${
                                item.isRescheduled
                                  ? "border-orange-400/40 bg-orange-50/30"
                                  : "border-green-600/20"
                              }`}
                            >
                              <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-5 gap-2 sm:gap-0 ${
                                item.isRescheduled
                                  ? "bg-custom-purple-100"
                                  : "bg-yellow-100"
                              }`}>
                                <label className="flex items-center gap-3 min-w-0 flex-1">
                                  <input
                                    type="checkbox"
                                    className="w-4 h-4 accent-green-600 cursor-pointer flex-shrink-0"
                                    checked={
                                      item.id
                                        ? selectedEventIds.includes(item.id)
                                        : false
                                    }
                                    onChange={(event) =>
                                      handleChange(event, item)
                                    }
                                  />
                                  <p className="text-sm sm:text-base/6 text-green-600 font-semibold capitalize truncate">
                                    {item?.summary}
                                  </p>
                                </label>
                                <p className="text-sm sm:text-base/6 text-green-600/70 font-semibold flex-shrink-0">
                                  Session :{" "}
                                  {index > 9 ? index + 1 : `0${index + 1}`}
                                </p>
                              </div>
                              <div className="p-3 sm:p-5">
                                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-0">
                                  <div className="space-y-2 min-w-0 flex-1">
                                    <p className="flex items-start sm:items-center gap-2 text-sm sm:text-base/6 text-primary/90 font-semibold">
                                      <Clock
                                        size={20}
                                        className="text-[#44B7E5] flex-shrink-0 mt-0.5 sm:mt-0"
                                      />{" "}
                                      <span className="break-words">
                                        {item.start?.timeZone ===
                                        "Asia/Kolkata" ? (
                                          <>
                                            {item.start?.dateTime ? (
                                              <>
                                                {formatDate(
                                                  item.start?.dateTime || ""
                                                )}{" "}
                                                | {/* Only Date */}
                                                {moment(
                                                  item.start?.dateTime
                                                ).format("hh:mm A")}{" "}
                                                {/* Only Time (HH:MM) */}
                                              </>
                                            ) : (
                                              <>
                                                {moment(item.start?.date).format(
                                                  "hh:mm"
                                                )}{" "}
                                                {/* Only Time (HH:MM) */}
                                              </>
                                            )}
                                          </>
                                        ) : (
                                          <>
                                            {item.start?.dateTime ? (
                                              <>
                                                {formatDate(
                                                  item.start?.dateTime || ""
                                                )}{" "}
                                                |{" "}
                                                {formatTime(
                                                  item.start?.dateTime || ""
                                                )}
                                              </>
                                            ) : (
                                              <>
                                                {formatDate(
                                                  item.start?.date || ""
                                                )}
                                              </>
                                            )}
                                          </>
                                        )}
                                      </span>
                                    </p>
                                    <p className="flex items-start sm:items-center gap-2 text-sm sm:text-base/6 text-primary/90">
                                      <EnvelopeSimple
                                        size={20}
                                        className="text-[#F6A002] flex-shrink-0 mt-0.5 sm:mt-0"
                                      />
                                      <span className="break-all">
                                        {attendees &&
                                          attendees?.length > 0 &&
                                          attendees[0].email}
                                      </span>
                                    </p>
                                  </div>
                                  <div className="relative group flex-shrink-0">
                                    <p className={`text-xs sm:text-sm/5 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full inline-block capitalize ${
                                      item.isRescheduled
                                        ? "custom-purple-600 bg-custom-purple-100"
                                        : "text-green-500 bg-green-200"
                                    }`}>
                                      {item.isRescheduled ? "Rescheduled" : item?.status} !
                                    </p>
                                  </div>
                                </div>
                                <hr className="my-3 sm:my-5" />
                                <p className="text-sm sm:text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                  <Repeat size={18} className="flex-shrink-0" />
                                  <span>Frequency: +{item?.eventIds?.length}</span>
                                </p>
                              </div>
                            </div>
                          );
                        }
                      )}
                  </div>
                  {resyncCalendarData?.syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
              {activeTab?.label === "Non Syncable Events" && (
                <div>
                  {/* Explanatory note for non-syncable events */}
                  <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm text-gray-800">
                      Oops! We couldn&apos;t find any guest in these sessions so we&apos;re assuming these are personal blocks on your calendar.
                    </p>
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4.5 py-4 sm:py-7.5">
                    {resyncCalendarData?.not_syncable && Array.isArray(resyncCalendarData.not_syncable) &&
                      resyncCalendarData.not_syncable.map(
                        (item: Item, index: number) => {
                          const attendees = (item.attendees && Array.isArray(item.attendees))
                            ? item.attendees.filter(
                                (attend) => attend.self != true
                              )
                            : [];
                        return (
                          <div
                            key={index}
                            className="border border-green-600/20 rounded-base overflow-hidden "
                          >
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-5 gap-2 sm:gap-0 bg-yellow-100">
                              <label className="flex items-center gap-3 min-w-0 flex-1">
                                {/* <input
                                type="checkbox"
                                className="w-4 h-4 accent-green-600 cursor-pointer"
                              /> */}
                                <p className="text-sm sm:text-base/6 text-green-600 font-semibold capitalize truncate">
                                  {item?.summary}
                                </p>
                              </label>
                              <p className="text-sm sm:text-base/6 text-green-600/70 font-semibold flex-shrink-0">
                                Session :{" "}
                                {index > 9 ? index + 1 : `0${index + 1}`}
                              </p>
                            </div>
                            <div className="p-3 sm:p-5">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-0">
                                <div className="space-y-2 min-w-0 flex-1">
                                  <p className="flex items-start sm:items-center gap-2 text-sm sm:text-base/6 text-primary/90 font-semibold">
                                    <Clock
                                      size={20}
                                      className="text-[#44B7E5] flex-shrink-0 mt-0.5 sm:mt-0"
                                    />{" "}
                                    <span className="break-words">
                                      {item.start?.timeZone === "Asia/Kolkata" ? (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {formatDate(
                                                item.start?.dateTime || ""
                                              )}{" "}
                                              | {/* Only Date */}
                                              {moment(
                                                item.start?.dateTime
                                              ).format("hh:mm A")}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          ) : (
                                            <>
                                              {moment(item.start?.date).format(
                                                "hh:mm"
                                              )}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {formatDate(
                                                item.start?.dateTime || ""
                                              )}{" "}
                                              |{" "}
                                              {formatTime(
                                                item.start?.dateTime || ""
                                              )}
                                            </>
                                          ) : (
                                            <>
                                              {formatDate(item.start?.date || "")}
                                            </>
                                          )}
                                        </>
                                      )}
                                    </span>
                                  </p>
                                  {attendees &&
                                    attendees?.length > 0 &&
                                    attendees[0].email && (
                                      <p className="flex items-start sm:items-center gap-2 text-sm sm:text-base/6 text-primary/90">
                                        <EnvelopeSimple
                                          size={20}
                                          className="text-[#F6A002] flex-shrink-0 mt-0.5 sm:mt-0"
                                        />
                                        <span className="break-all">
                                          {attendees &&
                                            attendees?.length > 0 &&
                                            attendees[0].email}
                                        </span>
                                      </p>
                                    )}
                                </div>
                                <div className="relative group flex-shrink-0">
                                  <p className="text-xs sm:text-sm/5 text-green-500 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full inline-block bg-green-200">
                                    {item?.status} !
                                  </p>
                                  <div className="absolute right-0 top-4 p-2.5 bg-white w-[202px] rounded-base shadow-[0px_4px_20.9px_0px_#0000001A] invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-300 z-10">
                                    <p className="text-xs_18 text-primary">
                                      This events does not have any{" "}
                                      <span className="font-semibold">
                                        attendees email
                                      </span>
                                      , hence this can not be synced!
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <hr className="my-3 sm:my-5" />
                              <p className="text-sm sm:text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                <Repeat size={18} className="flex-shrink-0" />
                                <span>Frequency: +{item?.eventIds?.length}</span>
                              </p>
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                  {resyncCalendarData?.not_syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
        {activeTab?.value === "Syncable Events" &&
          resyncCalendarData?.syncable?.length > 0 &&
          selectedEventIds?.length > 0 && (
            <div className="sticky bottom-0 z-40 bg-white py-3 px-4 sm:py-2.5 sm:px-0 flex justify-center sm:justify-end items-center border-t sm:border-t-0 shadow-lg sm:shadow-none">
              <Button
                variant="filled"
                className="mt-auto w-full sm:w-auto"
                // onClick={syncCalendarEvents}
                onClick={handleContinueClick}
                disabled={loading}
              >
                {loading ? "Loading..." : "Continue"}
              </Button>
            </div>
          )}

        {isModalOpen && (
          <Modal
            open={isModalOpen}
            handler={() => setIsModalOpen(false)}
            size="xl"
            email={resyncCalendarData?.syncable[0]?.attendees[0]?.email}
          >
            <div className="p-4 sm:p-5">
              <h2 className="text-lg sm:text-xl font-semibold mb-6 text-center sm:text-left">Required Details*</h2>

              {/* Mobile Card Layout */}
              <div className="block md:hidden space-y-6 mt-6 mb-8">
                {tableData.map((row, index) => (
                  <div key={index} className="bg-white border-2 border-gray-100 rounded-xl p-5 shadow-sm">
                    {/* Card Header */}
                    <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-100">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Session</span>
                      <span className="text-sm font-bold text-green-600 bg-green-50 px-3 py-1 rounded-full">#{index + 1}</span>
                    </div>

                    <div className="space-y-4">
                      {/* Session Name */}
                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                        <span className="text-xs font-medium text-gray-600 block mb-1 uppercase tracking-wide">Session Name</span>
                        <span className="text-sm font-semibold text-gray-800 break-words">{row.session}</span>
                      </div>

                      {/* Email */}
                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                        <span className="text-xs font-medium text-gray-600 block mb-1 uppercase tracking-wide">Email Address</span>
                        <span className="text-sm text-gray-700 break-all">{row.email}</span>
                      </div>

                      {/* Client Name */}
                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                        <span className="text-xs font-medium text-gray-600 block mb-2 uppercase tracking-wide">Client Name *</span>
                        {row.isPresent || isUpdateDisabled ? (
                          <span className="text-sm font-semibold text-gray-800">{row.clientName}</span>
                        ) : (
                          <div className="flex flex-col">
                            <input
                              type="text"
                              value={row.clientName || ""}
                              className="border-2 border-gray-200 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 px-3 py-3 w-full rounded-lg text-sm transition-all duration-200"
                              placeholder="Enter client name"
                              onChange={(e) => {
                                const updatedData = [...tableData];
                                updatedData[index] = { ...updatedData[index], clientName: e.target.value };
                                setTableData(updatedData);
                                // Clear errors for this field
                                setErrors((prevErrors) => {
                                  const newErrors = { ...prevErrors };
                                  if (e.target.value.trim() !== "") {
                                    delete newErrors[`${index}-clientName`];
                                  }
                                  return newErrors;
                                });
                              }}
                              disabled={isUpdateDisabled}
                            />
                            {errors[`${index}-clientName`] && (
                              <span className="text-red-500 text-xs mt-2 bg-red-50 p-2 rounded border-l-2 border-red-500">
                                {errors[`${index}-clientName`]}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Mobile Number */}
                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                        <span className="text-xs font-medium text-gray-600 block mb-2 uppercase tracking-wide">Mobile Number</span>
                        {row.isPresent || isUpdateDisabled ? (
                          <span className="text-sm font-semibold text-gray-800">{row.mobile}</span>
                        ) : (
                          <div>
                            <input
                              type="text"
                              inputMode="numeric"
                              pattern="[0-9]*"
                              value={row.mobile || ""}
                              className="border-2 border-gray-200 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 px-3 py-3 w-full rounded-lg text-sm transition-all duration-200"
                              placeholder="Enter 10-digit mobile number"
                              onChange={(e) => {
                                const updatedData = [...tableData];
                                updatedData[index] = { ...updatedData[index], mobile: Number(e.target.value) || 0 };
                                setTableData(updatedData);
                                // Clear/set errors for this field
                                setErrors((prevErrors) => {
                                  const newErrors = { ...prevErrors };
                                  const valStr = e.target.value.trim();
                                  if (valStr === "") {
                                    delete newErrors[`${index}-mobile`];
                                  } else if (!/^\d{10}$/.test(valStr)) {
                                    newErrors[`${index}-mobile`] = "Mobile number must be 10 digits";
                                  } else {
                                    delete newErrors[`${index}-mobile`];
                                  }
                                  return newErrors;
                                });
                              }}
                              disabled={isUpdateDisabled}
                              maxLength={10}
                            />
                            {errors[`${index}-mobile`] && (
                              <span className="text-red-500 text-xs mt-2 bg-red-50 p-2 rounded border-l-2 border-red-500">
                                {errors[`${index}-mobile`]}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Amount */}
                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                        <span className="text-xs font-medium text-gray-600 block mb-2 uppercase tracking-wide">Session Amount *</span>
                        {row.isPresent || isUpdateDisabled ? (
                          <span className="text-sm font-semibold text-gray-800">₹{row.amount}</span>
                        ) : (
                          <div className="flex flex-col">
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">₹</span>
                              <input
                                type="number"
                                value={row.amount || ""}
                                className="border-2 border-gray-200 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 pl-8 pr-3 py-3 w-full rounded-lg text-sm transition-all duration-200"
                                placeholder="Enter amount"
                                onChange={(e) => {
                                  const updatedData = [...tableData];
                                  updatedData[index] = { ...updatedData[index], amount: Number(e.target.value) || 0 };
                                  setTableData(updatedData);
                                  // Clear errors for this field
                                  setErrors((prevErrors) => {
                                    const newErrors = { ...prevErrors };
                                    if (!isNaN(Number(e.target.value)) && e.target.value !== "") {
                                      delete newErrors[`${index}-amount`];
                                    }
                                    return newErrors;
                                  });
                                }}
                                disabled={isUpdateDisabled}
                              />
                            </div>
                            {errors[`${index}-amount`] && (
                              <span className="text-red-500 text-xs mt-2 bg-red-50 p-2 rounded border-l-2 border-red-500">
                                {errors[`${index}-amount`]}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Desktop Table Layout */}
              <div className="hidden md:block overflow-x-auto py-4">
                <div className="rounded-2xl overflow-hidden border border-gray-300">
                  <table className="w-full bg-white border-separate min-w-[600px]">
                    <THeader data={CalendarTableHeader} />
                    <CalendarTBody
                      TableData={{ data: tableData }}
                      handleUpdate={handleUpdate}
                      handleSync={handleSync}
                      errors={errors}
                      setErrors={setErrors}
                      setTableData={setTableData}
                      isUpdateDisabled={isUpdateDisabled}
                    />
                  </table>
                </div>
              </div>

              <div className="flex justify-center gap-3 mt-8 mb-4">
              {tableData.every((row) => row.clientName && row.amount) && isUpdateDisabled ? (
                  <Button
                    onClick={handleSync}
                    className="px-6 py-3 text-white rounded-lg text-sm sm:text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                    disabled={loading && syncDisabled}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Syncing...
                      </div>
                    ) : (
                      "Sync Sessions"
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={handleUpdate}
                    className="px-6 py-3 text-white rounded-lg text-sm sm:text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                    disabled={updateDisabled}
                  >
                    {updateDisabled ? "Updated" : "Update Details"}
                  </Button>
                )}
              </div>
            </div>
          </Modal>
        )}

        {/* Confirmation Modal */}
        <SyncConfirmationModal
          open={isConfirmationModalOpen}
          onClose={() => setIsConfirmationModalOpen(false)}
          onConfirm={handleConfirmSync}
          loading={loading}
        />

        {/* Rescheduled Modal */}
        <RescheduledModal
          open={isRescheduledModalOpen}
          onClose={() => setIsRescheduledModalOpen(false)}
          onConfirm={handleRescheduledConfirm}
          loading={loading}
        />

        {/* Syncing Modal */}
        <SyncingModal open={isSyncingModalOpen} />

        {/* Conflict Modal */}
        <ConflictModal
          open={isConflictModalOpen}
          onClose={() => {
            setIsConflictModalOpen(false);
            setIsConfirmationModalOpen(false);
            // Clear conflict data when closing
            setConflictData({
              message: "",
              conflicts: []
            });
          }}
          message={conflictData.message}
          conflicts={conflictData.conflicts}
          isCalendarSync={true}
        />
      </div>
    </SettingLayout>
  );
};

export default CalendarSetting;
